import React from 'react';

const PhoneMockup = () => {
  return (
    <div className="relative">
      {/* Desktop Layout - Two Phones Side by Side */}
      <div className="hidden lg:block">
        <div className="relative">
          {/* Instagram Profile Phone (Left/Back) */}
          <div className="absolute -left-8 top-8 z-10">
            <div className="w-64 h-[520px] bg-black rounded-[2.5rem] p-2 shadow-2xl">
              <div className="w-full h-full bg-gray-900 rounded-[2rem] overflow-hidden">
                {/* Status Bar */}
                <div className="flex justify-between items-center px-6 py-3 text-white text-sm">
                  <span>9:41</span>
                  <div className="flex space-x-1">
                    <div className="w-4 h-2 bg-white rounded-sm"></div>
                    <div className="w-4 h-2 bg-white rounded-sm"></div>
                    <div className="w-4 h-2 bg-white rounded-sm"></div>
                  </div>
                </div>
                
                {/* Profile Header */}
                <div className="px-4 py-2">
                  <div className="flex items-center justify-between mb-4">
                    <div className="text-white text-lg font-semibold flex items-center">
                      <span>The.Curly.Yogini</span>
                      <div className="w-4 h-4 bg-blue-500 rounded-full ml-2 flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Profile Info */}
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-20 h-20 rounded-full overflow-hidden border-2 border-pink-500">
                      <div className="w-full h-full bg-gradient-to-br from-pink-400 to-orange-400"></div>
                    </div>
                    <div className="flex space-x-6 text-white text-center">
                      <div>
                        <div className="font-semibold">657</div>
                        <div className="text-xs text-gray-300">Posts</div>
                      </div>
                      <div>
                        <div className="font-semibold">10.2K</div>
                        <div className="text-xs text-gray-300">Followers</div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Username */}
                  <div className="text-white text-sm mb-2">The Curly Yogini</div>
                  
                  {/* Bio Link */}
                  <div className="bg-blue-600 text-white text-center py-1 px-3 rounded text-sm mb-3">
                    driply.chat/thecurlyyogini
                  </div>
                  
                  {/* Follow Button */}
                  <button className="w-full bg-blue-500 text-white py-2 rounded font-semibold">
                    Follow
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          {/* Chat Interface Phone (Right/Front) */}
          <div className="relative z-20 ml-32">
            <div className="w-80 h-[640px] bg-black rounded-[2.5rem] p-2 shadow-2xl">
              <div className="w-full h-full bg-purple-200 rounded-[2rem] overflow-hidden relative">
                {/* Chat Header */}
                <div className="bg-purple-300 px-4 py-3 flex items-center">
                  <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                    <div className="w-full h-full bg-gradient-to-br from-pink-400 to-orange-400"></div>
                  </div>
                  <div>
                    <div className="font-semibold text-black">The Curly Yogini</div>
                  </div>
                </div>
                
                {/* Chat Messages */}
                <div className="p-4 space-y-4">
                  <div className="bg-white rounded-2xl p-4 max-w-xs">
                    <p className="text-black text-sm">
                      Let's start stretching! Lessons? Times? Or anything else...
                    </p>
                  </div>
                  
                  {/* Course Cards */}
                  <div className="space-y-3">
                    <div className="bg-white rounded-xl p-3 flex justify-between items-center">
                      <div>
                        <div className="font-semibold text-black text-sm">Lessons</div>
                        <div className="text-gray-600 text-xs">All lessons here</div>
                      </div>
                    </div>
                    
                    <div className="bg-white rounded-xl p-3 flex justify-between items-center">
                      <div>
                        <div className="font-semibold text-black text-sm">Morning routine course</div>
                        <div className="text-gray-600 text-xs">10% off until today</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Chat Input */}
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="bg-white rounded-full px-4 py-3 flex items-center">
                    <input 
                      type="text" 
                      placeholder="Ask anything"
                      className="flex-1 outline-none text-sm text-gray-600"
                    />
                    <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center ml-2">
                      <span className="text-white text-sm">→</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Layout - Single Phone */}
      <div className="lg:hidden flex justify-center">
        <div className="relative">
          {/* Chat Interface Phone */}
          <div className="w-72 h-[580px] bg-black rounded-[2.5rem] p-2 shadow-2xl">
            <div className="w-full h-full bg-purple-200 rounded-[2rem] overflow-hidden relative">
              {/* Chat Header */}
              <div className="bg-purple-300 px-4 py-3 flex items-center">
                <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                  <div className="w-full h-full bg-gradient-to-br from-pink-400 to-orange-400"></div>
                </div>
                <div>
                  <div className="font-semibold text-black">The Curly Yogini</div>
                </div>
              </div>
              
              {/* Chat Messages */}
              <div className="p-4 space-y-4">
                <div className="bg-white rounded-2xl p-4 max-w-xs">
                  <p className="text-black text-sm">
                    Let's start stretching! Lessons? Times? Or anything else...
                  </p>
                </div>
                
                {/* Course Cards */}
                <div className="space-y-3">
                  <div className="bg-white rounded-xl p-3 flex justify-between items-center">
                    <div>
                      <div className="font-semibold text-black text-sm">Lessons</div>
                      <div className="text-gray-600 text-xs">All lessons here</div>
                    </div>
                  </div>
                  
                  <div className="bg-white rounded-xl p-3 flex justify-between items-center">
                    <div>
                      <div className="font-semibold text-black text-sm">Morning routine course</div>
                      <div className="text-gray-600 text-xs">10% off until today</div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Chat Input */}
              <div className="absolute bottom-4 left-4 right-4">
                <div className="bg-white rounded-full px-4 py-3 flex items-center">
                  <input 
                    type="text" 
                    placeholder="Ask anything"
                    className="flex-1 outline-none text-sm text-gray-600"
                  />
                  <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center ml-2">
                    <span className="text-white text-sm">→</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Instagram Profile Phone (Overlapping) */}
          <div className="absolute -left-16 top-12 z-10">
            <div className="w-56 h-[460px] bg-black rounded-[2.5rem] p-2 shadow-xl">
              <div className="w-full h-full bg-gray-900 rounded-[2rem] overflow-hidden">
                {/* Status Bar */}
                <div className="flex justify-between items-center px-4 py-2 text-white text-xs">
                  <span>9:41</span>
                </div>
                
                {/* Profile Header */}
                <div className="px-3 py-2">
                  <div className="text-white text-sm font-semibold flex items-center mb-3">
                    <span>The.Curly.Yogini</span>
                    <div className="w-3 h-3 bg-blue-500 rounded-full ml-1 flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  </div>
                  
                  {/* Profile Info */}
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-16 h-16 rounded-full overflow-hidden border-2 border-pink-500">
                      <div className="w-full h-full bg-gradient-to-br from-pink-400 to-orange-400"></div>
                    </div>
                    <div className="flex space-x-4 text-white text-center">
                      <div>
                        <div className="font-semibold text-sm">657</div>
                        <div className="text-xs text-gray-300">Posts</div>
                      </div>
                      <div>
                        <div className="font-semibold text-sm">10.2K</div>
                        <div className="text-xs text-gray-300">Followers</div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Username */}
                  <div className="text-white text-xs mb-2">The Curly Yogini</div>
                  
                  {/* Bio Link */}
                  <div className="bg-blue-600 text-white text-center py-1 px-2 rounded text-xs mb-2">
                    driply.chat/thecurlyyogini
                  </div>
                  
                  {/* Follow Button */}
                  <button className="w-full bg-blue-500 text-white py-1.5 rounded font-semibold text-xs">
                    Follow
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PhoneMockup;
