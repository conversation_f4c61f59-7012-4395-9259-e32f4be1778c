import React from 'react';
import PhoneMockup from './PhoneMockup';

const HeroSection = () => {
  return (
    <div className="min-h-screen bg-gray-100 relative overflow-hidden">
      {/* Desktop Layout */}
      <div className="hidden lg:block">
        <div className="container mx-auto px-6 py-12">
          <div className="grid grid-cols-2 gap-12 items-center min-h-[80vh]">
            {/* Left Content */}
            <div className="space-y-8">
              {/* Logo */}
              <div className="mb-12">
                <h1 className="text-2xl font-bold text-black tracking-wide">
                  DRIPLY
                </h1>
              </div>
              
              {/* Main Headline */}
              <div className="space-y-4">
                <h2 className="text-5xl font-bold text-black leading-tight">
                  Links Don't Sell.
                  <br />
                  Smart Conversations Do
                </h2>
                
                <p className="text-lg text-gray-700 leading-relaxed max-w-md">
                  Driply turns your Instagram bio link into an AI assistant 
                  that answers your audience questions, recommends 
                  your best offer & sells 24/7 for you!
                </p>
              </div>
              
              {/* CTA Button */}
              <button className="bg-lime-400 hover:bg-lime-500 text-black font-semibold px-8 py-4 rounded-lg text-lg transition-colors duration-200">
                Learn More
              </button>
            </div>
            
            {/* Right Content - Phone Mockups */}
            <div className="relative">
              <PhoneMockup />
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="lg:hidden">
        <div className="container mx-auto px-6 py-8">
          {/* Logo */}
          <div className="mb-8">
            <h1 className="text-xl font-bold text-black tracking-wide">
              DRIPLY
            </h1>
          </div>
          
          {/* Main Headline */}
          <div className="text-center space-y-6 mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-black leading-tight">
              Links Don't Sell.
              <br />
              Smart
              <br />
              Conversations Do
            </h2>
            
            <p className="text-base text-gray-700 leading-relaxed px-4">
              Driply turns your Instagram bio link 
              into an AI assistant that answers your 
              audience questions, recommends 
              your best offer & sells 24/7 for you!
            </p>
          </div>
          
          {/* Phone Mockups */}
          <div className="flex justify-center mb-12">
            <PhoneMockup />
          </div>
          
          {/* CTA Button */}
          <div className="px-4">
            <button className="w-full bg-lime-400 hover:bg-lime-500 text-black font-semibold py-4 rounded-lg text-lg transition-colors duration-200">
              Learn More
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
